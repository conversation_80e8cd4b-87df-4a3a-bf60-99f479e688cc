#!/usr/bin/env python3
"""
Test PDF URL extraction to verify the pipeline provides actionable URLs
"""

import json
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_pdf_url_extraction():
    """Test that we can extract actionable PDF URLs from notifications"""
    
    # Load notifications
    notifications_file = Path("rbi_notifications.json")
    if not notifications_file.exists():
        logger.error(f"Notifications file not found: {notifications_file}")
        return
    
    with open(notifications_file, 'r', encoding='utf-8') as f:
        notifications = json.load(f)
    
    logger.info(f"📁 Loaded {len(notifications)} notifications")
    
    # Test first 5 notifications for URL extraction
    test_notifications = notifications[:5]
    
    actionable_urls = []
    
    for i, notification in enumerate(test_notifications, 1):
        title = notification.get('Title', 'Unknown Title')
        pdf_link = notification.get('PDF Link', '')
        local_path = notification.get('Local Path', '')
        watermark = notification.get('Watermark', '')
        
        logger.info(f"\n🔄 Testing notification {i}: {title[:60]}...")
        
        # Check if we have actionable data
        has_pdf_link = bool(pdf_link)
        has_local_file = bool(local_path and Path(local_path).exists())
        has_watermark = bool(watermark)
        
        logger.info(f"   📄 PDF Link: {pdf_link}")
        logger.info(f"   📁 Local File: {'✅' if has_local_file else '❌'} {local_path}")
        logger.info(f"   🏷️ Watermark: {watermark}")
        
        # Simulate RSS description creation (like in real pipeline)
        rss_description = f"""
        <p>Section: {notification.get('Section', '')}</p>
        <p>Year: {notification.get('Year', '')}</p>
        <p class='head'>{title}</p>
        <p>Watermark: {watermark}</p>
        
        <p><strong>EXTRACTED_PDF_LINKS:</strong> {pdf_link}</p>
        <p><strong>PDF URL:</strong> <a href="{pdf_link}">{pdf_link}</a></p>
        
        <div class="extracted-info">
        <h3>=== EXTRACTED INFORMATION ===</h3>
        <p><strong>EXTRACTED_PDF_LINKS:</strong> {pdf_link}</p>
        <p><strong>NOTIFICATION_PDF_URL:</strong> {pdf_link}</p>
        <p><strong>DOCUMENT_REFERENCE_NUMBERS:</strong> {watermark}</p>
        </div>
        """
        
        # Check if this would be actionable for LLM
        actionable_score = 0
        if has_pdf_link:
            actionable_score += 3
        if has_local_file:
            actionable_score += 2
        if has_watermark:
            actionable_score += 1
            
        actionability = "HIGH" if actionable_score >= 5 else "MEDIUM" if actionable_score >= 3 else "LOW"
        
        logger.info(f"   🎯 Actionability: {actionability} (score: {actionable_score}/6)")
        
        if actionability in ["HIGH", "MEDIUM"]:
            actionable_urls.append({
                "title": title,
                "pdf_url": pdf_link,
                "actionability": actionability,
                "score": actionable_score,
                "has_content": has_local_file
            })
            
        # Extract reference numbers from watermark
        if watermark:
            # Look for common RBI reference patterns
            import re
            patterns = [
                r'RBI/\d{4}-\d{2}/\d+',
                r'FEMA\s+\d+\([A-Z]\)/\(\d+\)/\d{4}-[A-Z]+',
                r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/[\d\.]+/\d{4}-\d{2}',
                r'[A-Z]+\.\d+/[\d\.]+/\d{4}-\d{2}'
            ]
            
            found_refs = []
            for pattern in patterns:
                matches = re.findall(pattern, watermark)
                found_refs.extend(matches)
            
            if found_refs:
                logger.info(f"   📋 Reference Numbers: {', '.join(found_refs)}")
            else:
                logger.info(f"   📋 Reference Numbers: None found in standard patterns")
    
    # Summary
    logger.info(f"\n📊 ACTIONABILITY SUMMARY:")
    logger.info(f"   Total notifications tested: {len(test_notifications)}")
    logger.info(f"   Actionable notifications: {len(actionable_urls)}")
    logger.info(f"   Success rate: {len(actionable_urls)/len(test_notifications)*100:.1f}%")
    
    if actionable_urls:
        logger.info(f"\n✅ ACTIONABLE NOTIFICATIONS:")
        for item in actionable_urls:
            logger.info(f"   📄 {item['title'][:50]}...")
            logger.info(f"      URL: {item['pdf_url']}")
            logger.info(f"      Score: {item['actionability']} ({item['score']}/6)")
            logger.info(f"      Has Content: {'✅' if item['has_content'] else '❌'}")
    
    # Test URL format validation
    logger.info(f"\n🔍 URL FORMAT VALIDATION:")
    valid_urls = 0
    for item in actionable_urls:
        url = item['pdf_url']
        if url.startswith('https://rbidocs.rbi.org.in/rdocs/notification/PDFs/') and url.endswith('.PDF'):
            valid_urls += 1
            logger.info(f"   ✅ Valid: {url}")
        else:
            logger.info(f"   ❌ Invalid: {url}")
    
    logger.info(f"   Valid URL format: {valid_urls}/{len(actionable_urls)} ({valid_urls/len(actionable_urls)*100:.1f}%)")
    
    return len(actionable_urls) > 0


if __name__ == "__main__":
    logger.info("🚀 Starting PDF URL Extraction Test")
    logger.info("=" * 80)
    
    try:
        success = test_pdf_url_extraction()
        if success:
            logger.info("✅ PDF URL extraction test completed successfully!")
            logger.info("🎯 The pipeline should now provide actionable URLs for Qdrant updates")
        else:
            logger.error("❌ No actionable URLs found")
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(f"Stack trace: {traceback.format_exc()}")
