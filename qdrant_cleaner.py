import re
import csv
import logging
from typing import Any, Dict, List

from qdrant_client import QdrantClient
from qdrant_client.http import models

# === CONFIG ===
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
QDRANT_URL     = "http://localhost:6333"
COLLECTION     = "rbi_master_direction"
BATCH_SIZE     = 100
OUTPUT_CSV     = "document_ids_cleaned.csv"

DOCUMENT_TYPES = [
    "Master Direction", "Master Circular", "Notification",
    "Circular", "Directions"
]
DOC_TYPE_REGEX = re.compile(
    r"(" + "|".join(re.escape(dt) for dt in DOCUMENT_TYPES) + r")",
    flags=re.IGNORECASE
)
FALLBACK_SPLIT_REGEX = re.compile(
    r"^(.*?)\s+([A-Z]{2,}[\.\w/\-]+.*)$"
)

def clean_id(raw: str) -> str:
    s = raw.strip()
    s = re.sub(r"\s+", " ", s)
    s = re.sub(r"\s*/\s*", "/", s)
    s = re.sub(r"\s*\.\s*", ".", s)
    return s

def parse_document_id(cid: str):
    short_id, doc_type, long_code = "", "", ""
    m = DOC_TYPE_REGEX.search(cid)
    if m:
        doc_type = m.group(1).title()
        before = cid[:m.start()].strip(" .")
        after  = cid[m.end():].strip(" .")
        short_id, long_code = before, after
        dc = re.search(r"RBI/([A-Z]+)/", short_id)
        if dc and long_code.upper().startswith(dc.group(1)):
            long_code = re.sub(
                rf"^{dc.group(1)}\s*","", long_code, flags=re.IGNORECASE
            )
    else:
        fb = FALLBACK_SPLIT_REGEX.match(cid)
        if fb:
            short_id, long_code = fb.group(1).strip(" ."), fb.group(2).strip(" .")
        else:
            short_id = cid
    return short_id, doc_type, long_code

def unnest_metadata(payload: Dict[str, Any]) -> Dict[str, Any]:
    nested = payload.get("metadata")
    if isinstance(nested, dict):
        for k, v in nested.items():
            payload[k] = v
        del payload["metadata"]
    return payload

def enrich_metadata(payload: Dict[str, Any],
                    short_id: str,
                    doc_type: str,
                    long_code: str) -> Dict[str, Any]:
    meta = payload.get("metadata", {})
    if not isinstance(meta, dict):
        meta = {}
    meta.update({
        "short_id": short_id,
        "document_type": doc_type,
        "long_code": long_code
    })
    payload["metadata"] = meta
    return payload

def process_collection():
    client = QdrantClient(url=QDRANT_URL)
    scroll_offset = None
    csv_rows: List[Dict[str,str]] = []
    to_upsert: List[models.PointStruct] = []

    while True:
        # <-- Here we ask for vectors too -->
        batch, next_page = client.scroll(
            collection_name=COLLECTION,
            offset=scroll_offset,
            limit=BATCH_SIZE,
            with_payload=True,
            with_vectors=True   # <-- changed from False
        )

        for pt in batch:
            pl = pt.payload or {}
            pl = unnest_metadata(dict(pl))

            raw_id = pl.get("document_id") or pl.get("raw_document_id")
            if not raw_id:
                logging.warning(f"Point {pt.id} missing document_id, skipping")
                continue

            cid       = clean_id(str(raw_id))
            short_id, doc_type, long_code = parse_document_id(cid)

            csv_rows.append({
                "raw_document_id": str(raw_id),
                "cleaned_document_id": cid,
                "short_id": short_id,
                "document_type": doc_type,
                "long_code": long_code
            })

            pl = enrich_metadata(pl, short_id, doc_type, long_code)

            # <-- Include the vector when constructing PointStruct -->
            to_upsert.append(
                models.PointStruct(
                    id=pt.id,
                    vector=pt.vector,    # <-- new
                    payload=pl
                )
            )

            if len(to_upsert) >= BATCH_SIZE:
                client.upsert(
                    collection_name=COLLECTION,
                    points=to_upsert,
                    wait=True
                )
                logging.info(f"Upserted {len(to_upsert)} points")
                to_upsert.clear()

        if next_page is None:
            break
        scroll_offset = next_page

    if to_upsert:
        client.upsert(collection_name=COLLECTION, points=to_upsert, wait=True)
        logging.info(f"Upserted {len(to_upsert)} points")

    # Save CSV
    with open(OUTPUT_CSV, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(
            f,
            fieldnames=[
                "raw_document_id","cleaned_document_id",
                "short_id","document_type","long_code"
            ]
        )
        writer.writeheader()
        for row in csv_rows:
            writer.writerow(row)
    logging.info(f"Saved {len(csv_rows)} rows to {OUTPUT_CSV}")

if __name__ == "__main__":
    process_collection()
