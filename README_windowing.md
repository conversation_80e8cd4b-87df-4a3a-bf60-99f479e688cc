# PDF Windowing System for LLM Processing

This system provides a windowing approach for processing large PDF documents through LLMs, where each chunk includes context from the previous chunk's summary.

## Overview

The windowing system solves the problem of processing large documents that exceed LLM token limits by:

1. **Splitting documents into overlapping chunks** with configurable window size and overlap
2. **Passing previous chunk summaries as context** to maintain continuity
3. **Creating consolidated analysis** from all chunk results
4. **Preserving document flow** through intelligent boundary detection

## Key Features

- **Overlapping Windows**: Configurable window size and overlap to maintain context
- **Context Continuity**: Each chunk receives a summary of the previous chunk
- **Smart Boundaries**: Attempts to break at sentence boundaries for better context
- **Consolidated Analysis**: Combines all chunk analyses into a comprehensive summary
- **Flexible Input**: Supports both local PDF files and URLs

## Files

### Core Implementation
- `real_pipeline_test.py` - Main implementation with windowing functionality
- Contains the `RealPipelineTest` class with windowing methods

### Testing and Demonstration
- `test_windowed_pdf.py` - Test script for windowing with actual PDFs
- `demo_windowing.py` - Demonstration without requiring actual PDFs or API calls

## Usage

### 1. Basic Windowed PDF Processing

```bash
# Set your OpenAI API key
export OPENAI_API_KEY='your-key-here'

# Process a PDF with windowing
python real_pipeline_test.py windowed path/to/document.pdf

# With custom window size and overlap
python real_pipeline_test.py windowed path/to/document.pdf 3000 400
```

### 2. Testing Without API Calls

```bash
# Run the demonstration (no API key required)
python demo_windowing.py
```

### 3. Testing With Sample PDFs

```bash
# Test with any PDF in current directory
python test_windowed_pdf.py
```

## Configuration Parameters

### Window Size
- **Default**: 4000 characters
- **Purpose**: Size of each text chunk sent to LLM
- **Considerations**: Larger windows provide more context but use more tokens

### Overlap
- **Default**: 500 characters  
- **Purpose**: Overlap between consecutive chunks to maintain continuity
- **Considerations**: More overlap preserves context but increases processing

## How It Works

### 1. Text Extraction
```python
# Extract full text from PDF
full_text = extract_pdf_from_local_file(pdf_path)
```

### 2. Chunk Creation
```python
# Create overlapping windows
chunks = create_windowed_chunks(full_text, window_size=4000, overlap=500)
```

### 3. Context-Aware Processing
```python
previous_summary = None
for chunk in chunks:
    # Process chunk with previous context
    result = process_chunk_with_context(chunk, previous_summary)
    previous_summary = result["summary"]
```

### 4. Consolidation
```python
# Create final consolidated analysis
consolidated = create_consolidated_analysis(chunk_results)
```

## Example Output Structure

```json
{
  "pdf_source": "document.pdf",
  "total_length": 25000,
  "window_size": 4000,
  "overlap": 500,
  "total_chunks": 7,
  "chunk_results": [
    {
      "chunk_id": 0,
      "start_pos": 0,
      "end_pos": 4000,
      "summary": "Document introduction and scope...",
      "has_previous_context": false
    },
    {
      "chunk_id": 1,
      "start_pos": 3500,
      "end_pos": 7500,
      "summary": "Building on introduction, covers compliance requirements...",
      "has_previous_context": true
    }
  ],
  "consolidated_analysis": {
    "total_chunks_analyzed": 7,
    "successful_chunks": 7,
    "consolidated_summary": "Comprehensive document analysis..."
  }
}
```

## Key Methods

### `create_windowed_chunks(text, window_size, overlap)`
Creates overlapping text chunks with smart boundary detection.

### `process_chunk_with_context(chunk, previous_summary)`
Processes a single chunk with context from the previous chunk's summary.

### `process_pdf_with_windowing(pdf_path, window_size, overlap)`
Complete windowed processing of a PDF document.

### `create_consolidated_analysis(chunk_results)`
Creates a final consolidated analysis from all chunk results.

## Benefits

1. **Handles Large Documents**: Process documents of any size within LLM token limits
2. **Maintains Context**: Previous chunk summaries provide continuity
3. **Comprehensive Analysis**: Consolidated view of entire document
4. **Configurable**: Adjust window size and overlap for different use cases
5. **Robust**: Handles errors gracefully and provides detailed logging

## Use Cases

- **Regulatory Documents**: Process long RBI circulars and guidelines
- **Legal Documents**: Analyze contracts and legal texts
- **Research Papers**: Extract insights from academic papers
- **Technical Manuals**: Process detailed technical documentation
- **Financial Reports**: Analyze annual reports and financial statements

## Requirements

- Python 3.7+
- OpenAI API key (for actual LLM processing)
- PyMuPDF (fitz) for PDF text extraction
- Required Python packages: `openai`, `requests`, `pathlib`

## Error Handling

The system includes comprehensive error handling:
- Failed chunk processing doesn't stop the entire process
- Detailed logging for debugging
- Graceful fallbacks for extraction failures
- Consolidated error reporting

## Performance Considerations

- **Token Usage**: Larger windows and overlaps increase token consumption
- **Processing Time**: More chunks mean longer processing time
- **Memory Usage**: Large documents may require significant memory
- **API Limits**: Consider rate limits when processing multiple documents

## Future Enhancements

- Support for different document formats (Word, HTML, etc.)
- Advanced boundary detection (paragraph, section breaks)
- Parallel chunk processing
- Caching of chunk summaries
- Integration with vector databases for semantic search
