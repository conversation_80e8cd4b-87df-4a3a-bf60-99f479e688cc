#!/usr/bin/env python3
"""
Test script for windowed PDF processing functionality.
This demonstrates how to process a PDF document using overlapping windows
with context from previous chunks passed to the LLM.
"""

import os
import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_windowed_processing():
    """Test the windowed PDF processing functionality"""
    
    # Check for OpenAI API key
    if not os.getenv('OPENAI_API_KEY'):
        logger.error("❌ OPENAI_API_KEY environment variable is required")
        logger.info("   Please set it with: export OPENAI_API_KEY='your-key-here'")
        return False
    
    # Import the test class
    from real_pipeline_test import RealPipelineTest
    
    try:
        # Initialize the tester
        logger.info("🔧 Initializing windowed PDF processor...")
        tester = RealPipelineTest()
        
        # Example PDF path - you can change this to test with your own PDF
        # For testing, we'll look for any PDF files in the current directory
        pdf_files = list(Path(".").glob("*.pdf"))
        
        if not pdf_files:
            logger.warning("⚠️ No PDF files found in current directory")
            logger.info("   Please place a PDF file in the current directory or specify a path")
            
            # Create a sample text for demonstration
            sample_text = """
This is a sample regulatory document for testing windowed processing.

Section 1: Introduction
This document outlines the regulatory requirements for financial institutions.
The Reserve Bank of India (RBI) has issued these guidelines to ensure compliance
with banking regulations and to maintain financial stability.

Section 2: Compliance Requirements
All banks must adhere to the following requirements:
1. Maintain minimum capital adequacy ratios
2. Submit quarterly compliance reports
3. Implement risk management frameworks
4. Ensure customer data protection

Section 3: Implementation Timeline
The implementation of these guidelines shall be completed in phases:
Phase 1: Immediate compliance (within 30 days)
Phase 2: System upgrades (within 90 days)
Phase 3: Full implementation (within 180 days)

Section 4: Penalties
Non-compliance with these guidelines may result in:
- Monetary penalties up to Rs. 1 crore
- Suspension of banking license
- Other regulatory actions as deemed fit

Section 5: Conclusion
These guidelines are effective immediately and supersede all previous circulars
on this subject. Banks are advised to ensure strict compliance to avoid
regulatory action.
""" * 10  # Repeat to make it longer for windowing demonstration
            
            logger.info("📝 Testing with sample text instead...")
            
            # Test the windowing functionality directly
            chunks = tester.create_windowed_chunks(sample_text, window_size=800, overlap=100)
            logger.info(f"✅ Created {len(chunks)} chunks from sample text")
            
            # Process first few chunks to demonstrate
            previous_summary = None
            for i, chunk in enumerate(chunks[:3]):  # Process first 3 chunks
                logger.info(f"🔍 Processing chunk {i+1}...")
                result = tester.process_chunk_with_context(
                    chunk, 
                    previous_summary, 
                    chunk_index=i, 
                    total_chunks=len(chunks)
                )
                
                if "summary" in result:
                    previous_summary = result["summary"]
                    logger.info(f"✅ Chunk {i+1} processed successfully")
                    logger.info(f"   Summary length: {len(result['summary'])} characters")
                else:
                    logger.error(f"❌ Failed to process chunk {i+1}")
            
            return True
        
        # Use the first PDF found
        pdf_path = pdf_files[0]
        logger.info(f"📄 Testing with PDF: {pdf_path}")
        
        # Test windowed processing
        window_size = 3000  # Smaller window for testing
        overlap = 300
        
        logger.info(f"🪟 Starting windowed processing...")
        logger.info(f"   Window size: {window_size}")
        logger.info(f"   Overlap: {overlap}")
        
        result = tester.process_pdf_with_windowing(
            str(pdf_path), 
            is_local=True, 
            window_size=window_size, 
            overlap=overlap
        )
        
        if result.get("processing_status") == "success":
            logger.info("✅ Windowed PDF processing completed!")
            
            # Display results summary
            total_chunks = result.get("total_chunks", 0)
            consolidated = result.get("consolidated_analysis", {})
            successful_chunks = consolidated.get("successful_chunks", 0)
            
            logger.info(f"📊 Processing Summary:")
            logger.info(f"   Total document length: {result.get('total_length', 0)} characters")
            logger.info(f"   Total chunks created: {total_chunks}")
            logger.info(f"   Successfully processed: {successful_chunks}")
            logger.info(f"   Failed chunks: {consolidated.get('failed_chunks', 0)}")
            
            # Save results
            tester.windowed_results = [result]
            if tester.save_windowed_results("test_windowed_results.json"):
                logger.info("💾 Results saved to test_windowed_results.json")
            
            return True
        else:
            logger.error(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        logger.error(f"Stack trace: {traceback.format_exc()}")
        return False

def main():
    """Main function"""
    logger.info("🚀 Starting Windowed PDF Processing Test")
    logger.info("=" * 60)
    
    if test_windowed_processing():
        logger.info("✅ Test completed successfully!")
    else:
        logger.error("❌ Test failed!")

if __name__ == "__main__":
    main()
