#!/usr/bin/env python3
"""
Test PDF content extraction to verify the pipeline is reading PDFs correctly
"""

import json
import logging
import os
import sys
import requests
import tempfile
from pathlib import Path

# Add the DAGs directory to Python path
dags_path = Path(__file__).parent / "complai_knowledge_tracker" / "airflow" / "dags"
sys.path.insert(0, str(dags_path))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def simple_pdf_to_text(pdf_path):
    """Simple PDF to text extraction using PyMuPDF"""
    import fitz

    try:
        doc = fitz.open(pdf_path)
        text = ""

        for page_num in range(doc.page_count):
            page = doc.load_page(page_num)
            text += page.get_text()

        doc.close()
        return text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {e}")
        return None


def download_and_extract_pdf_content(pdf_url, max_chars=8000):
    """Download PDF and extract text content for LLM processing"""
    try:
        logger.info(f"📄 Downloading PDF for content extraction: {pdf_url}")

        # Download PDF
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(pdf_url, headers=headers, timeout=60)
        response.raise_for_status()

        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file.write(response.content)
            temp_pdf_path = temp_file.name

        try:
            # Extract text content from PDF using simple method
            logger.info(f"   🔍 Extracting text from PDF...")
            text_content = simple_pdf_to_text(temp_pdf_path)

            if not text_content:
                logger.error(f"   ❌ No text extracted from PDF")
                return None

            # Clean up the text
            text_content = text_content.strip()

            # Show first part of content
            preview = text_content[:500] + "..." if len(text_content) > 500 else text_content
            logger.info(f"   📝 Content preview: {preview}")

            # Limit content for LLM processing
            if len(text_content) > max_chars:
                text_content = text_content[:max_chars] + "... [CONTENT TRUNCATED]"

            logger.info(f"   ✅ Extracted {len(text_content)} characters from PDF")
            return text_content

        finally:
            # Clean up temporary file
            if os.path.exists(temp_pdf_path):
                os.unlink(temp_pdf_path)

    except Exception as e:
        logger.error(f"   ❌ Error extracting PDF content: {e}")
        import traceback
        logger.error(f"   📚 Stack trace: {traceback.format_exc()}")
        return None


def test_pdf_extraction():
    """Test PDF extraction with sample notifications using local files"""

    # Load notifications
    notifications_file = Path("rbi_notifications.json")
    if not notifications_file.exists():
        logger.error(f"Notifications file not found: {notifications_file}")
        return

    with open(notifications_file, 'r', encoding='utf-8') as f:
        notifications = json.load(f)

    logger.info(f"📁 Loaded {len(notifications)} notifications")

    # Test first 3 notifications using local files
    test_notifications = notifications[:3]

    for i, notification in enumerate(test_notifications, 1):
        title = notification.get('Title', 'Unknown Title')
        pdf_link = notification.get('PDF Link', '')
        local_path = notification.get('Local Path', '')

        logger.info(f"\n🔄 Testing notification {i}: {title[:60]}...")
        logger.info(f"   📄 PDF Link: {pdf_link}")
        logger.info(f"   📁 Local Path: {local_path}")

        # Try to read from local file first
        if local_path and Path(local_path).exists():
            logger.info(f"   📖 Reading from local file...")
            pdf_content = simple_pdf_to_text(local_path)

            if pdf_content:
                # Clean up the text
                pdf_content = pdf_content.strip()

                # Limit content for testing
                if len(pdf_content) > 3000:
                    pdf_content = pdf_content[:3000] + "... [CONTENT TRUNCATED]"

                logger.info(f"   ✅ Successfully extracted PDF content from local file")
                logger.info(f"   📊 Content length: {len(pdf_content)} characters")

                # Check if content contains useful information
                if any(keyword in pdf_content.lower() for keyword in ['rbi', 'reserve bank', 'circular', 'notification', 'direction']):
                    logger.info(f"   ✅ Content appears to be valid RBI document")
                else:
                    logger.warning(f"   ⚠️ Content may not be valid RBI document")

                # Show a sample of the content
                sample = pdf_content[:300] + "..." if len(pdf_content) > 300 else pdf_content
                logger.info(f"   📝 Sample content: {sample}")
            else:
                logger.error(f"   ❌ Failed to extract PDF content from local file")

        elif pdf_link:
            logger.info(f"   🌐 Local file not found, trying to download...")
            pdf_content = download_and_extract_pdf_content(pdf_link, max_chars=3000)

            if pdf_content:
                logger.info(f"   ✅ Successfully extracted PDF content from download")
                logger.info(f"   📊 Content length: {len(pdf_content)} characters")

                # Check if content contains useful information
                if any(keyword in pdf_content.lower() for keyword in ['rbi', 'reserve bank', 'circular', 'notification', 'direction']):
                    logger.info(f"   ✅ Content appears to be valid RBI document")
                else:
                    logger.warning(f"   ⚠️ Content may not be valid RBI document")

                # Show a sample of the content
                sample = pdf_content[:200] + "..." if len(pdf_content) > 200 else pdf_content
                logger.info(f"   📝 Sample content: {sample}")
            else:
                logger.error(f"   ❌ Failed to extract PDF content from download")
        else:
            logger.warning(f"   ⚠️ No PDF link or local path found")

    logger.info(f"\n✅ PDF extraction test completed")


if __name__ == "__main__":
    logger.info("🚀 Starting PDF Extraction Test")
    logger.info("=" * 80)
    
    try:
        test_pdf_extraction()
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(f"Stack trace: {traceback.format_exc()}")
