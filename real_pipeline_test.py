#!/usr/bin/env python3
"""
Real pipeline test that uses actual LLM processing functions.
This bypasses Airflow but uses the real notification processing logic.
"""

import json
import logging
import os
import sys
import requests
import tempfile
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Add the DAGs directory to Python path
dags_path = Path(__file__).parent / "complai_knowledge_tracker" / "airflow" / "dags"
sys.path.insert(0, str(dags_path))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class StandaloneConfig:
    """Standalone configuration that doesn't require Airflow Variables"""
    
    def __init__(self):
        # Set up OpenAI configuration from environment
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        # Mock other configurations
        self.qdrant_host = "localhost"
        self.database_uri = "mongodb://localhost:27017/"


class MockOpenAIManager:
    """OpenAI manager that works without Airflow Variables"""
    
    def __init__(self, api_key: str):
        from openai import OpenAI
        self.client = OpenAI(api_key=api_key)
    
    def get_client(self):
        return self.client
    
    def with_key_rotation(self, func):
        """Execute function with basic error handling"""
        return func()
    
    def get_completion(self, prompt: str, model: str = "gpt-4o-mini", temperature: float = 0.1) -> str:
        """Get a simple text completion from OpenAI"""
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Completion call failed: {e}")
            raise


class RealPipelineTest:
    """Test the real notification processing pipeline"""
    
    def __init__(self, notifications_file: str = "rbi_notifications.json"):
        self.notifications_file = Path(notifications_file)
        self.notifications = []
        self.results = []
        
        # Set up standalone configuration
        self.setup_environment()
        
        # Import and initialize the real notification processor
        self.setup_notification_processor()
    
    def setup_environment(self):
        """Set up the environment for standalone testing"""
        try:
            # Check for OpenAI API key
            if not os.getenv('OPENAI_API_KEY'):
                raise ValueError(
                    "OPENAI_API_KEY environment variable is required. "
                    "Please set it with: export OPENAI_API_KEY='your-key-here'"
                )
            
            # Create standalone config
            self.config = StandaloneConfig()
            logger.info("✅ Environment setup complete")
            
        except Exception as e:
            logger.error(f"❌ Environment setup failed: {e}")
            raise
    
    def setup_notification_processor(self):
        """Set up the real notification processor"""
        try:
            # Mock the config module to avoid Airflow dependency
            import sys
            from unittest.mock import MagicMock
            
            # Create mock config
            mock_config = MagicMock()
            mock_config.openai.get_next_key.return_value = self.config.openai_api_key
            
            # Replace the config import
            sys.modules['utils.config'] = MagicMock()
            sys.modules['utils.config'].config = mock_config
            
            # Create mock openai_manager
            openai_manager = MockOpenAIManager(self.config.openai_api_key)
            sys.modules['utils.openai_utils'] = MagicMock()
            sys.modules['utils.openai_utils'].openai_manager = openai_manager
            
            # Import the notification processor
            from prompts.notification_categorizer import (
                NotificationCategorizationResult,
                AffectedDocumentsResult,
                UpdateActionResult,
                NOTIFICATION_CATEGORIZER_PROMPT,
                AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT,
                UPDATE_ACTION_DETERMINER_PROMPT
            )
            
            # Create the real notification processor class
            class RealNotificationProcessor:
                def __init__(self):
                    self.client = openai_manager.get_client()
                    # Remove content length limit - pass entire notification to LLM
                
                def _make_llm_call(self, prompt: str, response_format, system_message: str = "You are an expert RBI regulatory analyst."):
                    """Make a structured LLM call with error handling"""
                    try:
                        model_name = "gpt-4.1"
                        logger.info(f"Making LLM call with model: {model_name}")
                        response = self.client.beta.chat.completions.parse(
                            model=model_name,
                            messages=[
                                {"role": "system", "content": system_message},
                                {"role": "user", "content": prompt}
                            ],
                            temperature=0.1,
                            response_format=response_format
                        )
                        
                        # Convert to dict
                        result = response.choices[0].message.parsed
                        return result.model_dump() if hasattr(result, 'model_dump') else result.__dict__
                        
                    except Exception as e:
                        logger.error(f"LLM call failed: {e}")
                        raise
                
                def analyze_notification(self, title: str, rss_description: str, link: str) -> dict:
                    """Categorize RBI notification and determine its impact on knowledge base"""
                    try:
                        logger.info(f"🔍 Starting notification analysis for: {title[:50]}...")
                        logger.info(f"📏 Notification length: {len(rss_description)} characters")

                        # Add reminder about enclosed directions in attached PDFs
                        enhanced_content = rss_description
                        if "attached" in rss_description.lower() or "enclosed" in rss_description.lower() or "herewith" in rss_description.lower():
                            enhanced_content += "\n\n=== IMPORTANT NOTE ===\nIf this notification mentions enclosed directions, guidelines, or regulations, please note that the enclosed direction/document is typically found in the attached PDF on the same notification page."

                        prompt = NOTIFICATION_CATEGORIZER_PROMPT.format(
                            title=title,
                            content=enhanced_content,
                            link=link
                        )

                        logger.info(f"📝 Prompt created, making LLM call...")
                        result = self._make_llm_call(prompt, NotificationCategorizationResult)
                        logger.info(f"✅ Notification categorized as: {result.get('category')} (confidence: {result.get('confidence')})")
                        return result

                    except Exception as e:
                        logger.error(f"Error analyzing notification: {e}")
                        return self._get_fallback_analysis()
                
                def extract_affected_documents(self, title: str, rss_description: str, category: str) -> dict:
                    """Extract specific documents affected by the notification"""
                    try:
                        logger.info(f"🔍 Extracting affected documents for: {title[:50]}...")
                        logger.info(f"📏 Notification length: {len(rss_description)} characters")

                        # Add reminder about enclosed directions in attached PDFs
                        enhanced_content = rss_description
                        if "attached" in rss_description.lower() or "enclosed" in rss_description.lower() or "herewith" in rss_description.lower():
                            enhanced_content += "\n\n=== IMPORTANT NOTE ===\nIf this notification mentions enclosed directions, guidelines, or regulations, please note that the enclosed direction/document is typically found in the attached PDF on the same notification page."

                        prompt = AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT.format(
                            title=title,
                            content=enhanced_content,
                            category=category
                        )

                        result = self._make_llm_call(
                            prompt,
                            AffectedDocumentsResult,
                            "You are an expert in RBI regulatory document analysis and knowledge base management."
                        )
                        logger.info(f"✅ Found {len(result.get('document_actions', []))} document actions")
                        return result

                    except Exception as e:
                        logger.error(f"Error extracting affected documents: {e}")
                        return self._get_fallback_affected_documents()
                
                def determine_update_actions(self, title: str, category: str, affected_documents: list, rss_description: str) -> dict:
                    """Determine specific actions needed for knowledge base updates"""
                    try:
                        logger.info(f"🎯 Determining update actions for: {title[:50]}...")
                        logger.info(f"📏 Notification length: {len(rss_description)} characters")

                        # Add reminder about enclosed directions in attached PDFs
                        enhanced_content = rss_description
                        if "attached" in rss_description.lower() or "enclosed" in rss_description.lower() or "herewith" in rss_description.lower():
                            enhanced_content += "\n\n=== IMPORTANT NOTE ===\nIf this notification mentions enclosed directions, guidelines, or regulations, please note that the enclosed direction/document is typically found in the attached PDF on the same notification page."

                        prompt = UPDATE_ACTION_DETERMINER_PROMPT.format(
                            title=title,
                            category=category,
                            affected_documents=json.dumps(affected_documents, indent=2),
                            content=enhanced_content
                        )

                        result = self._make_llm_call(
                            prompt,
                            UpdateActionResult,
                            "You are an expert in knowledge base management for RBI regulations."
                        )
                        logger.info(f"✅ Generated {len(result.get('actions', []))} update actions")
                        return result

                    except Exception as e:
                        logger.error(f"Error determining update actions: {e}")
                        return self._get_fallback_update_actions()
                
                def _get_fallback_analysis(self) -> dict:
                    """Return fallback when analysis fails"""
                    return {
                        "category": "Informational",
                        "confidence": "low",
                        "reasoning": "LLM analysis failed",
                        "affects_regulations": False,
                        "keywords_found": [],
                        "requires_kb_update": False
                    }
                
                def _get_fallback_affected_documents(self) -> dict:
                    """Return fallback when affected documents extraction fails"""
                    return {
                        "document_actions": [],
                        "document_keywords": [],
                        "has_new_document_link": False,
                        "new_document_url": "",
                        "rbi_links": [],
                        "processing_notes": "LLM analysis failed",
                        "requires_manual_review": True
                    }
                
                def _get_fallback_update_actions(self) -> dict:
                    """Return fallback when update action determination fails"""
                    return {
                        "actions": [],
                        "processing_notes": "LLM analysis failed",
                        "requires_manual_review": True
                    }
            
            # Create the processor instance
            self.notification_processor = RealNotificationProcessor()
            logger.info("✅ Real notification processor initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup notification processor: {e}")
            raise
    
    def load_notifications(self) -> bool:
        """Load notifications from JSON file"""
        try:
            if not self.notifications_file.exists():
                logger.error(f"Notifications file not found: {self.notifications_file}")
                return False
            
            with open(self.notifications_file, 'r', encoding='utf-8') as f:
                self.notifications = json.load(f)
            
            logger.info(f"📁 Loaded {len(self.notifications)} notifications")
            return True
            
        except Exception as e:
            logger.error(f"Error loading notifications: {e}")
            return False
    
    def extract_pdf_from_local_file(self, local_path, max_chars=8000):
        """Extract text content from local PDF file with enhanced metadata extraction"""
        import fitz

        try:
            logger.info(f"📄 Extracting content from local PDF: {local_path}")

            doc = fitz.open(local_path)
            text_content = ""

            for page_num in range(doc.page_count):
                page = doc.load_page(page_num)
                text_content += page.get_text()

            doc.close()

            # Clean up the text
            text_content = text_content.strip()

            # Extract notification codes from PDF content
            notification_codes = self.extract_notification_codes_from_text(text_content)

            # Limit content for LLM processing
            if len(text_content) > max_chars:
                text_content = text_content[:max_chars] + "... [CONTENT TRUNCATED]"

            logger.info(f"   ✅ Extracted {len(text_content)} characters from local PDF")

            # Log extracted codes
            if notification_codes.get('short_code') or notification_codes.get('long_code'):
                logger.info(f"   📋 Short code: {notification_codes.get('short_code', 'Not found')}")
                logger.info(f"   📋 Long code: {notification_codes.get('long_code', 'Not found')}")
                logger.info(f"   📅 Year from codes: {notification_codes.get('year', 'Not found')}")

            return text_content, notification_codes

        except Exception as e:
            logger.error(f"   ❌ Error extracting PDF content from local file: {e}")
            return None, {}

    def extract_notification_codes_from_text(self, text_content: str) -> dict:
        """Extract notification codes from PDF text content"""
        import re

        try:
            # Initialize result structure
            result = {
                'short_code': '',
                'long_code': '',
                'full_code': '',
                'year': '',
                'all_codes': []
            }

            # Pattern for short codes (RBI reference numbers)
            short_code_patterns = [
                r'RBI/\d{4}-\d{2}/\d+',           # RBI/2025-26/64
                r'RBI/[A-Z]+/\d{4}-\d{2}/\d+',    # RBI/DNBR/2016-17/42
                r'FEMA\s*\d+\([A-Z]*\)/\(\d+\)/\d{4}-[A-Z]+',  # FEMA 23(R)/(6)/2025-RB
                r'FEMA\s*\d+\([A-Z]*\)/\d{4}-[A-Z]+',          # FEMA 23(R)/2015-RB
            ]

            # Pattern for long codes (department codes)
            long_code_patterns = [
                r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}',  # DoR.MCS.REC.38/01.01.001/2025-26
                r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}',        # DBOD.No.Leg.BC.21/09.07.007/2002
                r'\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}',                # .PD.004/03.10.119/2016-17
                r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+',              # DPSS.CO.PD.No.123/02.14.003
            ]

            # Extract short codes
            for pattern in short_code_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                for match in matches:
                    result['all_codes'].append(match)
                    if not result['short_code']:  # Take first match as primary
                        result['short_code'] = match

            # Extract long codes
            for pattern in long_code_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                for match in matches:
                    result['all_codes'].append(match)
                    if not result['long_code']:  # Take first match as primary
                        result['long_code'] = match

            # Extract year from codes
            year_matches = re.findall(r'\d{4}-\d{2}', ' '.join(result['all_codes']))
            if year_matches:
                # Convert 2025-26 format to 2025
                year_part = year_matches[0].split('-')[0]
                result['year'] = year_part
            else:
                # Fallback: look for 4-digit years in codes
                year_matches = re.findall(r'\d{4}', ' '.join(result['all_codes']))
                if year_matches:
                    # Take the most recent year that makes sense (2020-2030 range)
                    valid_years = [y for y in year_matches if 2020 <= int(y) <= 2030]
                    if valid_years:
                        result['year'] = valid_years[0]

            # Create full code by combining short and long codes
            if result['short_code'] and result['long_code']:
                result['full_code'] = f"{result['short_code']}; {result['long_code']}"
            elif result['short_code']:
                result['full_code'] = result['short_code']
            elif result['long_code']:
                result['full_code'] = result['long_code']

            # Remove duplicates from all_codes
            result['all_codes'] = list(set(result['all_codes']))

            return result

        except Exception as e:
            logger.error(f"Error extracting notification codes: {e}")
            return {
                'short_code': '',
                'long_code': '',
                'full_code': '',
                'year': '',
                'all_codes': []
            }

    def classify_chunk_for_indexing(self, chunk_content: str, document_metadata: dict) -> dict:
        """
        Classify chunks and determine if they should be indexed to Qdrant.

        Args:
            chunk_content: The text content of the chunk
            document_metadata: Metadata about the document

        Returns:
            dict: {
                'classification': str,  # 'regulatory', 'informational', 'administrative', 'withdrawn'
                'should_index': bool,   # Whether to index this chunk
                'reasoning': str        # Explanation for the classification
            }
        """
        try:
            # Initialize result
            result = {
                'classification': 'informational',
                'should_index': True,
                'reasoning': 'Default classification'
            }

            # Convert content to lowercase for analysis
            content_lower = chunk_content.lower()

            # Check for withdrawn/repealed documents
            withdrawn_indicators = [
                'withdrawn', 'repealed', 'superseded', 'cancelled', 'revoked',
                'no longer applicable', 'stands withdrawn', 'hereby repealed',
                'will stand withdrawn', 'previous.*superseded'
            ]

            if any(indicator in content_lower for indicator in withdrawn_indicators):
                result.update({
                    'classification': 'withdrawn',
                    'should_index': False,
                    'reasoning': 'Document marked as withdrawn/repealed'
                })
                return result

            # Check for regulatory content indicators
            regulatory_indicators = [
                'regulation', 'directive', 'guideline', 'instruction', 'circular',
                'master direction', 'notification', 'amendment', 'compliance',
                'shall', 'must', 'required', 'mandatory', 'prohibited', 'penalty',
                'effective from', 'applicable to', 'banks shall', 'nbfcs shall'
            ]

            regulatory_count = sum(1 for indicator in regulatory_indicators if indicator in content_lower)

            # Check for informational content indicators
            informational_indicators = [
                'information', 'clarification', 'announcement', 'press release',
                'speech', 'statement', 'report', 'publication', 'data',
                'statistics', 'for information', 'please note', 'fyi'
            ]

            informational_count = sum(1 for indicator in informational_indicators if indicator in content_lower)

            # Check for administrative content indicators
            administrative_indicators = [
                'tender', 'recruitment', 'appointment', 'office', 'administrative',
                'meeting', 'conference', 'seminar', 'training', 'holiday',
                'office hours', 'contact', 'address'
            ]

            administrative_count = sum(1 for indicator in administrative_indicators if indicator in content_lower)

            # Classify based on indicator counts and document metadata
            document_type = document_metadata.get('document_type', '').lower()

            # High regulatory content
            if regulatory_count >= 3 or document_type in ['master_direction', 'master_circular', 'notification']:
                result.update({
                    'classification': 'regulatory',
                    'should_index': True,
                    'reasoning': f'High regulatory content (score: {regulatory_count})'
                })

            # Administrative content
            elif administrative_count >= 2 or document_type in ['tender']:
                result.update({
                    'classification': 'administrative',
                    'should_index': False,
                    'reasoning': f'Administrative content (score: {administrative_count})'
                })

            # Informational content with some regulatory relevance
            elif informational_count >= 2 and regulatory_count >= 1:
                result.update({
                    'classification': 'informational',
                    'should_index': True,
                    'reasoning': f'Informational with regulatory relevance (info: {informational_count}, reg: {regulatory_count})'
                })

            # Pure informational content
            elif informational_count >= 2:
                result.update({
                    'classification': 'informational',
                    'should_index': True,
                    'reasoning': f'Pure informational content (score: {informational_count})'
                })

            # Default to informational if unclear
            else:
                result.update({
                    'classification': 'informational',
                    'should_index': True,
                    'reasoning': 'Default classification - unclear content type'
                })

            return result

        except Exception as e:
            logger.error(f"Error classifying chunk: {e}")
            return {
                'classification': 'informational',
                'should_index': True,
                'reasoning': f'Classification error: {str(e)}'
            }

    def create_windowed_chunks(self, text_content: str, window_size: int = 4000, overlap: int = 500) -> List[Dict]:
        """Create overlapping windows of text content for LLM processing with classification"""
        if not text_content or len(text_content) <= window_size:
            chunk = {
                "chunk_id": 0,
                "content": text_content,
                "start_pos": 0,
                "end_pos": len(text_content or "")
            }
            # Add classification
            classification = self.classify_chunk_for_indexing(text_content, {})
            chunk.update(classification)
            return [chunk]

        chunks = []
        start = 0
        chunk_id = 0

        while start < len(text_content):
            end = min(start + window_size, len(text_content))

            # Try to break at sentence boundaries for better context
            if end < len(text_content):
                # Look for sentence endings within the last 200 characters
                search_start = max(end - 200, start)
                sentence_end = text_content.rfind('.', search_start, end)
                if sentence_end > start:
                    end = sentence_end + 1

            chunk_content = text_content[start:end].strip()

            chunk = {
                "chunk_id": chunk_id,
                "content": chunk_content,
                "start_pos": start,
                "end_pos": end,
                "length": len(chunk_content)
            }

            # Add classification for each chunk
            classification = self.classify_chunk_for_indexing(chunk_content, {})
            chunk.update(classification)

            chunks.append(chunk)

            chunk_id += 1
            start = end - overlap  # Create overlap for context continuity

            # Prevent infinite loop
            if start >= end:
                break

        # Log classification summary
        should_index_count = sum(1 for chunk in chunks if chunk.get('should_index', True))
        logger.info(f"   📑 Created {len(chunks)} windowed chunks with {overlap} char overlap")
        logger.info(f"   📊 Classification: {should_index_count}/{len(chunks)} chunks should be indexed")

        return chunks

    def process_chunk_with_context(self, chunk: Dict, previous_summary: str = None, chunk_index: int = 0, total_chunks: int = 1) -> Dict:
        """Process a single chunk with context from previous chunk summary"""
        try:
            chunk_content = chunk["content"]
            chunk_id = chunk["chunk_id"]

            logger.info(f"   🔍 Processing chunk {chunk_id + 1}/{total_chunks} (chars: {chunk['length']})")

            # Create context-aware prompt
            context_prompt = f"""
You are an expert RBI regulatory analyst processing a document in chunks.

CHUNK INFORMATION:
- Chunk {chunk_index + 1} of {total_chunks}
- Position: characters {chunk['start_pos']}-{chunk['end_pos']}
- Length: {chunk['length']} characters

"""

            if previous_summary:
                context_prompt += f"""
PREVIOUS CHUNK SUMMARY:
{previous_summary}

CURRENT CHUNK TO ANALYZE:
{chunk_content}

Please analyze this chunk considering the context from the previous summary. Focus on:
1. Key regulatory concepts and requirements
2. Important dates, deadlines, or effective dates
3. Affected entities or institutions
4. Compliance obligations or prohibitions
5. Any references to other regulations or circulars
6. Continuity with previous content

Provide a comprehensive summary that builds upon the previous context.
"""
            else:
                context_prompt += f"""
DOCUMENT CHUNK TO ANALYZE:
{chunk_content}

This is the first chunk of the document. Please analyze and provide:
1. Key regulatory concepts and requirements
2. Important dates, deadlines, or effective dates
3. Affected entities or institutions
4. Compliance obligations or prohibitions
5. Any references to other regulations or circulars
6. Document purpose and scope

Provide a comprehensive summary of this opening section.
"""

            # Get LLM analysis
            openai_manager = MockOpenAIManager(self.config.openai_api_key)
            summary = openai_manager.get_completion(
                context_prompt,
                model="gpt-4o-mini",
                temperature=0.1
            )

            result = {
                "chunk_id": chunk_id,
                "chunk_index": chunk_index,
                "start_pos": chunk["start_pos"],
                "end_pos": chunk["end_pos"],
                "length": chunk["length"],
                "summary": summary,
                "has_previous_context": previous_summary is not None,
                "processing_time": datetime.now().isoformat()
            }

            logger.info(f"   ✅ Chunk {chunk_id + 1} processed successfully")
            return result

        except Exception as e:
            logger.error(f"   ❌ Error processing chunk {chunk.get('chunk_id', 'unknown')}: {e}")
            return {
                "chunk_id": chunk.get("chunk_id", -1),
                "chunk_index": chunk_index,
                "error": str(e),
                "processing_time": datetime.now().isoformat()
            }

    def process_pdf_with_windowing(self, pdf_path_or_url: str, is_local: bool = True, window_size: int = 4000, overlap: int = 500) -> Dict:
        """Process entire PDF using windowing approach with context from previous chunks"""
        try:
            logger.info(f"🪟 Starting windowed PDF processing: {pdf_path_or_url}")

            # Extract full text content
            if is_local:
                full_text = self.extract_pdf_from_local_file(pdf_path_or_url, max_chars=None)  # No limit for windowing
            else:
                full_text = self.download_and_extract_pdf_content(pdf_path_or_url, max_chars=None)  # No limit for windowing

            if not full_text:
                logger.error("❌ Failed to extract PDF content")
                return {"error": "Failed to extract PDF content", "processing_time": datetime.now().isoformat()}

            logger.info(f"📄 Extracted {len(full_text)} characters total")

            # Create windowed chunks
            chunks = self.create_windowed_chunks(full_text, window_size, overlap)

            # Process each chunk with context from previous
            chunk_results = []
            previous_summary = None

            for i, chunk in enumerate(chunks):
                chunk_result = self.process_chunk_with_context(
                    chunk,
                    previous_summary,
                    chunk_index=i,
                    total_chunks=len(chunks)
                )
                chunk_results.append(chunk_result)

                # Use this chunk's summary as context for next chunk
                if "summary" in chunk_result:
                    previous_summary = chunk_result["summary"]

            # Create final consolidated analysis
            logger.info("🔄 Creating consolidated document analysis...")
            consolidated_analysis = self.create_consolidated_analysis(chunk_results, full_text[:1000])  # First 1000 chars for context

            result = {
                "pdf_source": pdf_path_or_url,
                "is_local_file": is_local,
                "total_length": len(full_text),
                "window_size": window_size,
                "overlap": overlap,
                "total_chunks": len(chunks),
                "chunk_results": chunk_results,
                "consolidated_analysis": consolidated_analysis,
                "processing_time": datetime.now().isoformat(),
                "processing_status": "success"
            }

            logger.info(f"✅ Windowed PDF processing completed: {len(chunks)} chunks processed")
            return result

        except Exception as e:
            logger.error(f"❌ Error in windowed PDF processing: {e}")
            return {
                "pdf_source": pdf_path_or_url,
                "error": str(e),
                "processing_time": datetime.now().isoformat(),
                "processing_status": "failed"
            }

    def create_consolidated_analysis(self, chunk_results: List[Dict], document_preview: str) -> Dict:
        """Create a consolidated analysis from all chunk results"""
        try:
            # Collect all successful summaries
            summaries = []
            for result in chunk_results:
                if "summary" in result and result["summary"]:
                    summaries.append(f"Chunk {result['chunk_index'] + 1}: {result['summary']}")

            if not summaries:
                return {"error": "No successful chunk summaries to consolidate"}

            # Create consolidation prompt
            consolidation_prompt = f"""
You are an expert RBI regulatory analyst. You have analyzed a document in {len(summaries)} chunks.
Please create a comprehensive consolidated analysis that synthesizes all the chunk summaries into a coherent overview.

DOCUMENT PREVIEW (first 1000 characters):
{document_preview}

CHUNK SUMMARIES TO CONSOLIDATE:
{chr(10).join(summaries)}

Please provide a consolidated analysis that includes:
1. DOCUMENT OVERVIEW: Overall purpose and scope
2. KEY REGULATORY REQUIREMENTS: Main compliance obligations
3. IMPORTANT DATES: Deadlines, effective dates, implementation timelines
4. AFFECTED ENTITIES: Who must comply with these regulations
5. COMPLIANCE ACTIONS: Specific actions required
6. REFERENCES: Other regulations, circulars, or guidelines mentioned
7. SUMMARY: Concise overall summary of the document

Ensure the analysis flows logically and captures the complete regulatory picture.
"""

            # Get consolidated analysis from LLM
            openai_manager = MockOpenAIManager(self.config.openai_api_key)
            consolidated_summary = openai_manager.get_completion(
                consolidation_prompt,
                model="gpt-4o-mini",
                temperature=0.1
            )

            return {
                "total_chunks_analyzed": len(summaries),
                "successful_chunks": len([r for r in chunk_results if "summary" in r]),
                "failed_chunks": len([r for r in chunk_results if "error" in r]),
                "consolidated_summary": consolidated_summary,
                "processing_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ Error creating consolidated analysis: {e}")
            return {
                "error": str(e),
                "processing_time": datetime.now().isoformat()
            }

    def download_and_extract_pdf_content(self, pdf_url, max_chars=8000):
        """Download PDF and extract text content for LLM processing"""
        import requests
        import tempfile
        import os
        import sys

        # Add the utils path for PDF processing
        utils_path = Path(__file__).parent / "complai_knowledge_tracker" / "airflow" / "dags" / "utils"
        sys.path.insert(0, str(utils_path))

        try:
            from pdf_utils import parse_pdf_to_html_ast
            from bs4 import BeautifulSoup

            logger.info(f"📄 Downloading PDF for content extraction: {pdf_url}")

            # Download PDF
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(pdf_url, headers=headers, timeout=60)
            response.raise_for_status()

            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                temp_file.write(response.content)
                temp_pdf_path = temp_file.name

            try:
                # Extract HTML content from PDF
                html_content = parse_pdf_to_html_ast(temp_pdf_path)

                # Parse and clean the content
                soup = BeautifulSoup(str(html_content), 'html.parser')
                text_content = soup.get_text(separator=' ', strip=True)

                # Limit content for LLM processing
                if len(text_content) > max_chars:
                    text_content = text_content[:max_chars] + "... [CONTENT TRUNCATED]"

                logger.info(f"   ✅ Extracted {len(text_content)} characters from PDF")
                return text_content

            finally:
                # Clean up temporary file
                if os.path.exists(temp_pdf_path):
                    os.unlink(temp_pdf_path)

        except Exception as e:
            logger.error(f"   ❌ Error extracting PDF content: {e}")
            return None

    def process_notification(self, notification: Dict, index: int) -> Dict:
        """Process a single notification with real LLM analysis and enhanced metadata extraction"""
        title = notification.get('Title', 'Unknown Title')
        pdf_link = notification.get('PDF Link', '')
        year = notification.get('Year', '')
        watermark = notification.get('Watermark', '')

        logger.info(f"\n🔄 Processing notification {index}: {title[:60]}...")
        logger.info(f"   📄 PDF Link: {pdf_link}")
        logger.info(f"   📅 Original Year: {year}")

        # Step 1: Extract actual PDF content with notification codes (try local file first, then download)
        pdf_content = None
        notification_codes = {}
        local_path = notification.get('Local Path', '')

        if local_path and Path(local_path).exists():
            logger.info(f"   📖 Reading PDF content from local file: {local_path}")
            pdf_content, notification_codes = self.extract_pdf_from_local_file(local_path, max_chars=12000)
        elif pdf_link:
            logger.info(f"   📖 Downloading and extracting PDF content...")
            pdf_content = self.download_and_extract_pdf_content(pdf_link, max_chars=12000)
            if pdf_content:
                notification_codes = self.extract_notification_codes_from_text(pdf_content)

        # Fix year parsing bug: Use year from notification codes if available and different
        corrected_year = year
        if notification_codes.get('year') and notification_codes['year'] != year:
            corrected_year = notification_codes['year']
            logger.info(f"   🔧 Year corrected from {year} to {corrected_year} based on PDF codes")
        elif not year and notification_codes.get('year'):
            corrected_year = notification_codes['year']
            logger.info(f"   🔧 Year extracted from PDF codes: {corrected_year}")

        # Detect if document is withdrawn/repealed
        is_withdrawn = False
        withdrawal_reason = ""
        if pdf_content:
            content_lower = pdf_content.lower()
            withdrawn_indicators = [
                'withdrawn', 'repealed', 'superseded', 'cancelled', 'revoked',
                'no longer applicable', 'stands withdrawn', 'hereby repealed'
            ]
            for indicator in withdrawn_indicators:
                if indicator in content_lower:
                    is_withdrawn = True
                    withdrawal_reason = f"Document contains '{indicator}' indicator"
                    logger.info(f"   ⚠️ Document marked as withdrawn: {withdrawal_reason}")
                    break

        # Create RSS description with actual PDF content AND PDF URL in the format expected by prompts
        if pdf_content:
            rss_description = f"""
            <p>Section: {notification.get('Section', '')}</p>
            <p>Year: {year}</p>
            <p>Date: {notification.get('Date', '')}</p>
            <p class='head'>{title}</p>
            <p>Watermark: {watermark}</p>

            <p><strong>EXTRACTED_PDF_LINKS:</strong> {pdf_link}</p>
            <p><strong>PDF URL:</strong> <a href="{pdf_link}">{pdf_link}</a></p>

            <div class="pdf-content">
            <h3>NOTIFICATION CONTENT:</h3>
            {pdf_content}
            </div>

            <div class="extracted-info">
            <h3>=== EXTRACTED INFORMATION ===</h3>
            <p><strong>EXTRACTED_PDF_LINKS:</strong> {pdf_link}</p>
            <p><strong>NOTIFICATION_PDF_URL:</strong> {pdf_link}</p>
            <p><strong>DOCUMENT_REFERENCE_NUMBERS:</strong> {watermark}</p>
            </div>
            """
            logger.info(f"   ✅ Created RSS description with {len(pdf_content)} chars of PDF content + PDF URL")
        else:
            # Fallback to basic description with PDF URL
            rss_description = f"""
            <p>Section: {notification.get('Section', '')}</p>
            <p>Year: {year}</p>
            <p>Date: {notification.get('Date', '')}</p>
            <p class='head'>{title}</p>
            <p>Watermark: {watermark}</p>

            <p><strong>EXTRACTED_PDF_LINKS:</strong> {pdf_link}</p>
            <p><strong>PDF URL:</strong> <a href="{pdf_link}">{pdf_link}</a></p>

            <div class="extracted-info">
            <h3>=== EXTRACTED INFORMATION ===</h3>
            <p><strong>EXTRACTED_PDF_LINKS:</strong> {pdf_link}</p>
            <p><strong>NOTIFICATION_PDF_URL:</strong> {pdf_link}</p>
            <p><strong>DOCUMENT_REFERENCE_NUMBERS:</strong> {watermark}</p>
            </div>
            """
            logger.warning(f"   ⚠️ Using basic RSS description with PDF URL (no PDF content)")

        try:
            # Step 2: Analyze notification with real LLM (now with actual PDF content)
            analysis_result = self.notification_processor.analyze_notification(title, rss_description, pdf_link)
            
            category = analysis_result.get('category', 'Unknown')
            confidence = analysis_result.get('confidence', 'Unknown')
            requires_kb_update = analysis_result.get('requires_kb_update', False)
            
            logger.info(f"   📂 Category: {category}")
            logger.info(f"   🎯 Confidence: {confidence}")
            logger.info(f"   ⚡ Requires KB Update: {requires_kb_update}")
            
            # Step 2: Extract affected documents if KB update required
            affected_docs = {}
            update_actions = {}
            
            if requires_kb_update:
                logger.info(f"   🔍 Extracting affected documents...")
                affected_docs = self.notification_processor.extract_affected_documents(title, rss_description, category)
                
                document_actions = affected_docs.get('document_actions', [])
                logger.info(f"   📄 Found {len(document_actions)} document actions")
                
                # Step 3: Determine update actions
                logger.info(f"   🎯 Determining update actions...")
                update_actions = self.notification_processor.determine_update_actions(title, category, document_actions, rss_description)
                
                actions = update_actions.get('actions', [])
                logger.info(f"   ⚡ Generated {len(actions)} update actions")
            
            # Compile result with enhanced metadata
            result = {
                "notification_id": index,
                "title": title,
                "year": year,
                "corrected_year": corrected_year,
                "year_source": "pdf_codes" if corrected_year != year else "original",
                "pdf_link": pdf_link,
                "watermark": watermark,
                "notification_codes": notification_codes,
                "is_withdrawn": is_withdrawn,
                "withdrawal_reason": withdrawal_reason,
                "document_status": "withdrawn" if is_withdrawn else "active",
                "should_index": not is_withdrawn,  # Don't index withdrawn documents
                "llm_analysis": analysis_result,
                "affected_documents": affected_docs,
                "update_actions": update_actions,
                "processing_status": "success",
                "processing_time": datetime.now().isoformat()
            }
            
            logger.info(f"   ✅ Successfully processed notification {index}")
            return result
            
        except Exception as e:
            logger.error(f"   ❌ Error processing notification {index}: {e}")
            return {
                "notification_id": index,
                "title": title,
                "year": year,
                "corrected_year": corrected_year if 'corrected_year' in locals() else year,
                "year_source": "pdf_codes" if 'corrected_year' in locals() and corrected_year != year else "original",
                "pdf_link": pdf_link,
                "watermark": watermark,
                "notification_codes": notification_codes if 'notification_codes' in locals() else {},
                "is_withdrawn": is_withdrawn if 'is_withdrawn' in locals() else False,
                "withdrawal_reason": withdrawal_reason if 'withdrawal_reason' in locals() else "",
                "document_status": "withdrawn" if 'is_withdrawn' in locals() and is_withdrawn else "active",
                "should_index": not (is_withdrawn if 'is_withdrawn' in locals() else False),
                "processing_status": "failed",
                "error": str(e),
                "processing_time": datetime.now().isoformat()
            }
    
    def run_real_test(self, max_notifications: int = 5) -> bool:
        """Run real LLM processing test"""
        if not self.load_notifications():
            return False

        # Limit notifications for testing
        test_notifications = self.notifications[:50][0-max_notifications:]

        logger.info(f"🚀 Running REAL LLM processing test on {len(test_notifications)} notifications")
        logger.info("⚠️  This will use OpenAI API credits!")

        self.results = []
        for i, notification in enumerate(test_notifications, 1):
            result = self.process_notification(notification, i)
            self.results.append(result)

        return True

    def run_windowed_pdf_test(self, pdf_path: str, window_size: int = 4000, overlap: int = 500) -> bool:
        """Run windowed PDF processing test on a single PDF"""
        try:
            logger.info(f"🪟 Running windowed PDF processing test")
            logger.info(f"   📄 PDF: {pdf_path}")
            logger.info(f"   🔧 Window size: {window_size}, Overlap: {overlap}")
            logger.info("⚠️  This will use OpenAI API credits!")

            # Check if file exists
            if not Path(pdf_path).exists():
                logger.error(f"❌ PDF file not found: {pdf_path}")
                return False

            # Process PDF with windowing
            result = self.process_pdf_with_windowing(
                pdf_path,
                is_local=True,
                window_size=window_size,
                overlap=overlap
            )

            # Store result
            self.windowed_results = [result]

            if result.get("processing_status") == "success":
                logger.info("✅ Windowed PDF processing completed successfully!")

                # Display summary
                total_chunks = result.get("total_chunks", 0)
                successful_chunks = result.get("consolidated_analysis", {}).get("successful_chunks", 0)
                logger.info(f"📊 Summary: {successful_chunks}/{total_chunks} chunks processed successfully")

                return True
            else:
                logger.error(f"❌ Windowed PDF processing failed: {result.get('error', 'Unknown error')}")
                return False

        except Exception as e:
            logger.error(f"❌ Windowed PDF test failed: {e}")
            return False
    
    def save_results(self, output_file: str = "real_pipeline_results.json") -> bool:
        """Save results to JSON file"""
        try:
            output = {
                "metadata": {
                    "test_date": datetime.now().isoformat(),
                    "total_notifications": len(self.results),
                    "test_type": "real_llm_processing",
                    "version": "1.0"
                },
                "results": self.results
            }

            output_path = Path(output_file)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(output, f, indent=2, ensure_ascii=False)

            logger.info(f"💾 Results saved to: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving results: {e}")
            return False

    def save_windowed_results(self, output_file: str = "windowed_pdf_results.json") -> bool:
        """Save windowed PDF processing results to JSON file"""
        try:
            if not hasattr(self, 'windowed_results') or not self.windowed_results:
                logger.error("❌ No windowed results to save")
                return False

            output = {
                "metadata": {
                    "test_date": datetime.now().isoformat(),
                    "test_type": "windowed_pdf_processing",
                    "version": "1.0",
                    "total_pdfs_processed": len(self.windowed_results)
                },
                "results": self.windowed_results
            }

            output_path = Path(output_file)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(output, f, indent=2, ensure_ascii=False)

            logger.info(f"💾 Windowed results saved to: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving windowed results: {e}")
            return False


def main():
    """Main function"""
    import sys

    logger.info("🚀 Starting REAL Pipeline Test")
    logger.info("=" * 80)

    # Check for OpenAI API key
    if not os.getenv('OPENAI_API_KEY'):
        logger.error("❌ OPENAI_API_KEY environment variable is required")
        logger.info("   Please set it with: export OPENAI_API_KEY='your-key-here'")
        return

    # Check command line arguments for test type
    test_type = "notifications"  # default
    pdf_path = None
    window_size = 4000
    overlap = 500

    if len(sys.argv) > 1:
        if sys.argv[1] == "windowed" and len(sys.argv) > 2:
            test_type = "windowed"
            pdf_path = sys.argv[2]
            if len(sys.argv) > 3:
                window_size = int(sys.argv[3])
            if len(sys.argv) > 4:
                overlap = int(sys.argv[4])
        elif sys.argv[1] == "help":
            print("Usage:")
            print("  python real_pipeline_test.py                           # Run notification processing")
            print("  python real_pipeline_test.py windowed <pdf_path>       # Run windowed PDF processing")
            print("  python real_pipeline_test.py windowed <pdf_path> <window_size> <overlap>")
            print("  python real_pipeline_test.py help                      # Show this help")
            return

    try:
        # Initialize tester
        tester = RealPipelineTest()

        if test_type == "windowed":
            logger.info(f"🪟 Running WINDOWED PDF processing test")
            logger.info(f"   📄 PDF: {pdf_path}")
            logger.info(f"   🔧 Window size: {window_size}, Overlap: {overlap}")

            # Run windowed PDF test
            if not tester.run_windowed_pdf_test(pdf_path, window_size, overlap):
                logger.error("❌ Windowed PDF test failed")
                return

            # Save windowed results
            if tester.save_windowed_results():
                logger.info("✅ Windowed PDF processing completed successfully!")
            else:
                logger.error("❌ Failed to save windowed results")

        else:
            logger.info(f"📋 Running NOTIFICATION processing test")

            # Run real test (start with 15 notifications to test)
            if not tester.run_real_test(max_notifications=15):
                logger.error("❌ Test failed")
                return

            # Save results
            if tester.save_results():
                logger.info("✅ Real pipeline test completed successfully!")

                # Display summary
                successful = sum(1 for r in tester.results if r.get('processing_status') == 'success')
                total = len(tester.results)
                logger.info(f"📊 Summary: {successful}/{total} notifications processed successfully")
            else:
                logger.error("❌ Failed to save results")

    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(f"Stack trace: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
